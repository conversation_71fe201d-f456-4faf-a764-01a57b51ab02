package com.example.repository;

import com.example.entity.UserFarm;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for UserFarm entity operations
 * Provides custom queries for user-farm mapping management
 */
@Repository
public interface UserFarmRepository extends JpaRepository<UserFarm, String> {

    /**
     * Find all active user-farm mappings for a specific user
     */
    @Query("SELECT uf FROM UserFarm uf WHERE uf.userId = :userId AND uf.isDeleted = false")
    List<UserFarm> findActiveByUserId(@Param("userId") String userId);

    /**
     * Find all user-farm mappings for a specific user (including deleted)
     */
    List<UserFarm> findByUserId(String userId);

    /**
     * Find active user-farm mapping by user ID and farm ID
     */
    @Query("SELECT uf FROM UserFarm uf WHERE uf.userId = :userId AND uf.farmId = :farmId AND uf.isDeleted = false")
    Optional<UserFarm> findActiveByUserIdAndFarmId(@Param("userId") String userId, @Param("farmId") String farmId);

    /**
     * Find user-farm mapping by user ID and farm ID (including deleted)
     */
    Optional<UserFarm> findByUserIdAndFarmId(String userId, String farmId);

    /**
     * Find all active user-farm mappings for a specific farm
     */
    @Query("SELECT uf FROM UserFarm uf WHERE uf.farmId = :farmId AND uf.isDeleted = false")
    List<UserFarm> findActiveByFarmId(@Param("farmId") String farmId);

    /**
     * Check if an active mapping exists between user and farm
     */
    @Query("SELECT COUNT(uf) > 0 FROM UserFarm uf WHERE uf.userId = :userId AND uf.farmId = :farmId AND uf.isDeleted = false")
    boolean existsActiveMapping(@Param("userId") String userId, @Param("farmId") String farmId);

    /**
     * Soft delete all active mappings for a user
     */
    @Modifying
    @Query("UPDATE UserFarm uf SET uf.isDeleted = true, uf.isActive = false, uf.updatedBy = :updatedBy WHERE uf.userId = :userId AND uf.isDeleted = false")
    int softDeleteAllByUserId(@Param("userId") String userId, @Param("updatedBy") Long updatedBy);

    /**
     * Soft delete specific user-farm mapping
     */
    @Modifying
    @Query("UPDATE UserFarm uf SET uf.isDeleted = true, uf.isActive = false, uf.updatedBy = :updatedBy WHERE uf.userId = :userId AND uf.farmId = :farmId AND uf.isDeleted = false")
    int softDeleteByUserIdAndFarmId(@Param("userId") String userId, @Param("farmId") String farmId, @Param("updatedBy") Long updatedBy);

    /**
     * Find all user-farm mappings by tenant ID
     */
    @Query("SELECT uf FROM UserFarm uf WHERE uf.tenantId = :tenantId AND uf.isDeleted = false")
    List<UserFarm> findActiveByTenantId(@Param("tenantId") Long tenantId);

    /**
     * Count active mappings for a user
     */
    @Query("SELECT COUNT(uf) FROM UserFarm uf WHERE uf.userId = :userId AND uf.isDeleted = false")
    long countActiveByUserId(@Param("userId") String userId);

    /**
     * Find all mappings that need to be updated (for admin operations)
     */
    @Query("SELECT uf FROM UserFarm uf WHERE uf.userId = :userId AND uf.farmId IN :farmIds")
    List<UserFarm> findByUserIdAndFarmIdIn(@Param("userId") String userId, @Param("farmIds") List<String> farmIds);
}

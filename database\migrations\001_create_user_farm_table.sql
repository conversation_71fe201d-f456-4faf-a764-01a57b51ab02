-- Migration: Create UserFarm table for user-farm mapping
-- This table manages the relationship between users and farms with soft delete support

CREATE TABLE user_farm (
    id VARCHAR(50) PRIMARY KEY,
    tenant_id BIGINT NOT NULL DEFAULT 0,
    slug VARCHAR(50) NOT NULL,
    created_by BIGINT NOT NULL DEFAULT 0,
    updated_by BIGINT NOT NULL DEFAULT 0,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    user_id VARCHAR(50) NOT NULL,
    farm_id VARCHAR(50) NOT NULL,
    farm_name VARCHAR(255),
    total_count BIGINT DEFAULT 0,
    farm_code VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create indexes for better query performance
CREATE INDEX idx_user_farm_user_id ON user_farm(user_id);
CREATE INDEX idx_user_farm_farm_id ON user_farm(farm_id);
CREATE INDEX idx_user_farm_tenant_id ON user_farm(tenant_id);
CREATE INDEX idx_user_farm_is_deleted ON user_farm(is_deleted);
CREATE INDEX idx_user_farm_is_active ON user_farm(is_active);

-- Composite index for common queries
CREATE INDEX idx_user_farm_user_farm ON user_farm(user_id, farm_id);
CREATE INDEX idx_user_farm_active_mapping ON user_farm(user_id, farm_id, is_deleted, is_active);

-- Unique constraint to prevent duplicate active mappings
CREATE UNIQUE INDEX idx_user_farm_unique_active 
ON user_farm(user_id, farm_id) 
WHERE is_deleted = FALSE;

-- Add comments for documentation
COMMENT ON TABLE user_farm IS 'Manages the mapping between users and farms with soft delete support';
COMMENT ON COLUMN user_farm.id IS 'Primary key identifier';
COMMENT ON COLUMN user_farm.tenant_id IS 'Tenant identifier for multi-tenancy';
COMMENT ON COLUMN user_farm.slug IS 'URL-friendly identifier';
COMMENT ON COLUMN user_farm.created_by IS 'User ID who created this record';
COMMENT ON COLUMN user_farm.updated_by IS 'User ID who last updated this record';
COMMENT ON COLUMN user_farm.is_deleted IS 'Soft delete flag - TRUE means deleted';
COMMENT ON COLUMN user_farm.is_active IS 'Active status flag';
COMMENT ON COLUMN user_farm.user_id IS 'Reference to user';
COMMENT ON COLUMN user_farm.farm_id IS 'Reference to farm';
COMMENT ON COLUMN user_farm.farm_name IS 'Cached farm name for performance';
COMMENT ON COLUMN user_farm.total_count IS 'Total count related to this mapping';
COMMENT ON COLUMN user_farm.farm_code IS 'Farm code identifier';

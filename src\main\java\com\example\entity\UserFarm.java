package com.example.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

/**
 * Entity representing the mapping between users and farms
 * Supports soft delete functionality and multi-tenancy
 */
@Entity
@Table(name = "user_farm", 
       indexes = {
           @Index(name = "idx_user_farm_user_id", columnList = "user_id"),
           @Index(name = "idx_user_farm_farm_id", columnList = "farm_id"),
           @Index(name = "idx_user_farm_tenant_id", columnList = "tenant_id"),
           @Index(name = "idx_user_farm_is_deleted", columnList = "is_deleted"),
           @Index(name = "idx_user_farm_is_active", columnList = "is_active"),
           @Index(name = "idx_user_farm_user_farm", columnList = "user_id, farm_id"),
           @Index(name = "idx_user_farm_active_mapping", columnList = "user_id, farm_id, is_deleted, is_active")
       })
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserFarm {

    @Id
    @Column(name = "id", length = 50)
    private String id;

    @Column(name = "tenant_id", nullable = false)
    @Builder.Default
    private Long tenantId = 0L;

    @Column(name = "slug", length = 50, nullable = false)
    private String slug;

    @Column(name = "created_by", nullable = false)
    @Builder.Default
    private Long createdBy = 0L;

    @Column(name = "updated_by", nullable = false)
    @Builder.Default
    private Long updatedBy = 0L;

    @Column(name = "is_deleted", nullable = false)
    @Builder.Default
    private Boolean isDeleted = false;

    @Column(name = "is_active", nullable = false)
    @Builder.Default
    private Boolean isActive = true;

    @Column(name = "user_id", length = 50, nullable = false)
    private String userId;

    @Column(name = "farm_id", length = 50, nullable = false)
    private String farmId;

    @Column(name = "farm_name")
    private String farmName;

    @Column(name = "total_count")
    @Builder.Default
    private Long totalCount = 0L;

    @Column(name = "farm_code", length = 50)
    private String farmCode;

    @CreationTimestamp
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    /**
     * Soft delete this user-farm mapping
     */
    public void softDelete() {
        this.isDeleted = true;
        this.isActive = false;
    }

    /**
     * Activate this user-farm mapping
     */
    public void activate() {
        this.isDeleted = false;
        this.isActive = true;
    }

    /**
     * Check if this mapping is currently active (not deleted and active)
     */
    public boolean isCurrentlyActive() {
        return !isDeleted && isActive;
    }
}
